"""
NocoDB Service for Common Class API

A comprehensive service for interacting with NocoDB API including:
- Fetching records from tables
- Updating records
- Creating new records
- Database operations

Author: Common Class API
Version: 1.0.0
"""

import logging
from typing import Dict, List, Any, Optional
import httpx
from app.core.config import get_settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Nocodb:
    def __init__(self, **kwargs):
        """
        Initialize NocoDB service with configuration.

        Args:
            token: NocoDB authentication token
            base_url: NocoDB base URL (default: https://app.nocodb.com)
            **kwargs: Additional configuration parameters
        """
        settings = get_settings()
        self.default_token = kwargs.get("token") or kwargs.get("oauth_token") or settings.nocodb_token
        self.base_url = kwargs.get("base_url") or settings.nocodb_base_url or "http://www.amibigeye.com:8080"

        if not self.default_token:
            logger.warning("NocoDB token not provided. Some operations may fail.")

    def _get_headers(self, token: Optional[str] = None) -> Dict[str, str]:
        """Get headers for NocoDB API requests."""
        auth_token = token or self.default_token
        if not auth_token:
            raise ValueError("NocoDB token is required")

        return {
            "xc-auth": auth_token,
            "Content-Type": "application/json"
        }

    async def fetch_record(
        self,
        table_id: str,
        record_id: str,
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Fetch a specific record from a NocoDB table.

        Args:
            table_id: The NocoDB table ID
            record_id: The record ID to fetch
            token: Optional NocoDB token (uses default if not provided)

        Returns:
            Dict containing the record data or error information

        Example:
            await nocodb.fetch_record("movxrk3kdcbkyo8", "123")
        """
        try:
            headers = self._get_headers(token)
            url = f"{self.base_url}/api/v2/tables/{table_id}/records/{record_id}"

            async with httpx.AsyncClient() as client:
                response = await client.get(url, headers=headers)

                if response.status_code == 200:
                    data = response.json()
                    return {
                        "status": "success",
                        "message": f"Successfully fetched record {record_id}",
                        "record": data,
                        "data": data  # For backward compatibility
                    }
                else:
                    logger.error(f"NocoDB fetch error: {response.status_code} - {response.text}")
                    return {
                        "status": "error",
                        "error": f"HTTP {response.status_code}: {response.text}",
                        "message": f"Failed to fetch record {record_id}"
                    }

        except Exception as e:
            logger.error(f"NocoDB fetch record error: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "message": f"Failed to fetch record {record_id}"
            }

    async def update_record(
        self,
        table_id: str,
        record_id: str,
        data: Dict[str, Any],
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Update a specific record in a NocoDB table.

        Args:
            table_id: The NocoDB table ID
            record_id: The record ID to update
            data: Dictionary containing the fields to update
            token: Optional NocoDB token (uses default if not provided)

        Returns:
            Dict containing the updated record data or error information

        Example:
            await nocodb.update_record("movxrk3kdcbkyo8", "123", {"adjust_content": "Generated content"})
        """
        try:
            headers = self._get_headers(token)
            url = f"{self.base_url}/api/v2/tables/{table_id}/records/{record_id}"

            async with httpx.AsyncClient() as client:
                response = await client.patch(url, headers=headers, json=data)

                if response.status_code == 200:
                    updated_data = response.json()
                    return {
                        "status": "success",
                        "message": f"Successfully updated record {record_id}",
                        "record": updated_data,
                        "data": updated_data,  # For backward compatibility
                        "updated_fields": list(data.keys())
                    }
                else:
                    logger.error(f"NocoDB update error: {response.status_code} - {response.text}")
                    return {
                        "status": "error",
                        "error": f"HTTP {response.status_code}: {response.text}",
                        "message": f"Failed to update record {record_id}"
                    }

        except Exception as e:
            logger.error(f"NocoDB update record error: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "message": f"Failed to update record {record_id}"
            }

    # Legacy method for backward compatibility
    async def update(self, **kwargs):
        """
        Legacy update method for backward compatibility.
        Redirects to update_record if table_id and record_id are provided.
        """
        table_id = kwargs.get("table_id")
        record_id = kwargs.get("record_id") or kwargs.get("id")

        if table_id and record_id:
            # Remove table_id and record_id from data
            data = {k: v for k, v in kwargs.items() if k not in ["table_id", "record_id", "id", "token"]}
            return await self.update_record(
                table_id=table_id,
                record_id=record_id,
                data=data,
                token=kwargs.get("token")
            )
        else:
            return {"status": "ok", "echo": kwargs, "message": "Legacy update method - provide table_id and record_id for actual updates"}

    async def test_method(self, **kwargs) -> Dict[str, Any]:
        """Simple test method that doesn't make external API calls."""
        return {
            "status": "success",
            "message": "NocoDB service is working!",
            "test": True,
            "base_url": self.base_url,
            "has_token": bool(self.default_token),
            "kwargs": kwargs
        }

    async def get_service_info(self, **kwargs) -> Dict[str, Any]:
        """
        Get information about the NocoDB service and available methods.

        Returns:
            Dict containing service information

        Example:
            nocodb.get_service_info()
        """
        return {
            "service_name": "NocoDB API Service",
            "version": "1.0.0",
            "description": "NocoDB database service for database operations",
            "features": [
                "Fetch records from tables",
                "Update existing records",
                "Database operations",
                "Secure token authentication"
            ],
            "base_url": self.base_url,
            "has_token": bool(self.default_token),
            "available_methods": [
                "fetch_record(table_id, record_id)",
                "update_record(table_id, record_id, data)",
                "update(**kwargs) - legacy method",
                "test_method()",
                "get_service_info()"
            ],
            "example_usage": {
                "fetch_record": {
                    "description": "Fetch a record from a table",
                    "example": {
                        "table_id": "movxrk3kdcbkyo8",
                        "record_id": "123"
                    }
                },
                "update_record": {
                    "description": "Update a record in a table",
                    "example": {
                        "table_id": "movxrk3kdcbkyo8",
                        "record_id": "123",
                        "data": {"adjust_content": "Generated content"}
                    }
                }
            }
        }


