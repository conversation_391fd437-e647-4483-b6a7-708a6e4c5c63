#!/usr/bin/env python3
"""
Test script for NocoDB + OpenAI integration
"""

import asyncio
import httpx
import json

# Configuration
API_BASE_URL = "http://127.0.0.1:8000"
API_TOKEN = "8a2c1cf846ba5e1245bfb3cc31a6d60a779ca49f02b65ddce1a355e91d01f8bd"
HEADERS = {
    "Authorization": f"Bearer {API_TOKEN}",
    "Content-Type": "application/json"
}

async def test_nocodb_service():
    """Test the NocoDB service"""
    print("🧪 Testing NocoDB service...")
    
    async with httpx.AsyncClient() as client:
        # Test the test_method
        response = await client.post(
            f"{API_BASE_URL}/nocodb.test_method",
            headers=HEADERS,
            json={}
        )
        
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        return response.status_code == 200

async def test_openai_service():
    """Test the OpenAI service"""
    print("\n🧪 Testing OpenAI service...")
    
    async with httpx.AsyncClient() as client:
        # Test the test_method
        response = await client.post(
            f"{API_BASE_URL}/openai.test_method",
            headers=HEADERS,
            json={}
        )
        
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        return response.status_code == 200

async def test_openai_chat():
    """Test OpenAI chat functionality"""
    print("\n🧪 Testing OpenAI chat...")
    
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{API_BASE_URL}/openai.chat",
            headers=HEADERS,
            json={"message": "Write a short poem about technology"}
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Generated content: {result.get('content', 'No content')[:100]}...")
        else:
            print(f"Error: {response.text}")
        
        return response.status_code == 200

async def test_integration_endpoint():
    """Test the new integration endpoint with mock data"""
    print("\n🧪 Testing integration endpoint (will fail with real NocoDB call, but should show proper error handling)...")
    
    async with httpx.AsyncClient() as client:
        # This will fail because we don't have a real record, but it should show proper error handling
        response = await client.get(
            f"{API_BASE_URL}/openai.generate?id=test123",
            headers=HEADERS
        )
        
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        # We expect this to fail with a proper error message
        return response.status_code == 200 or "fetch_record" in response.text

async def main():
    """Run all tests"""
    print("🚀 Starting Common Class API Integration Tests\n")
    
    tests = [
        ("NocoDB Service", test_nocodb_service),
        ("OpenAI Service", test_openai_service),
        ("OpenAI Chat", test_openai_chat),
        ("Integration Endpoint", test_integration_endpoint)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
            print(f"✅ {test_name}: {'PASSED' if result else 'FAILED'}")
        except Exception as e:
            results.append((test_name, False))
            print(f"❌ {test_name}: ERROR - {str(e)}")
        
        print("-" * 50)
    
    print("\n📊 Test Summary:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed!")
    else:
        print("⚠️  Some tests failed - check the output above")

if __name__ == "__main__":
    asyncio.run(main())
