from functools import lru_cache
from dotenv import load_dotenv
from pydantic_settings import BaseSettings, SettingsConfigDict

load_dotenv()

class Settings(BaseSettings):
    api_token: str
    openai_token: str | None = None
    nocodb_token: str | None = None
    nocodb_base_url: str | None = "http://www.amibigeye.com:8080"
    minio_key: str | None = None
    minio_secret: str | None = None
    line_token: str | None = None
    facebook_token: str | None = None

    model_config = SettingsConfigDict(env_file=".env", env_file_encoding="utf-8")

@lru_cache
def get_settings():
    return Settings()
